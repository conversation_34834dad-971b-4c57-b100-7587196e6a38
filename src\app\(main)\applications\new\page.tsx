"use client";

import React, { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { useSession } from "next-auth/react";
import Link from "next/link";
import { ContentLayout } from "@/components/admin-panel/content-layout";
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import { Button } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { ArrowLeft } from "lucide-react";
import { UserSelectionForm } from "@/components/applications/create/user-selection-form";
import { ProductSelectionForm } from "@/components/applications/create/product-selection-form";
import { DiscountApplicationForm } from "@/components/applications/create/discount-application-form";
import { PaymentSelectionForm } from "@/components/applications/create/payment-selection-form";
import { AgentAssignmentForm } from "@/components/applications/create/agent-assignment-form";

import {
  useProfile,
  useCreateApplication,
  useCreateUserForApplication,
} from "@/hooks/use-query";

interface ApplicationFormData {
  // User data
  userId?: string;
  createNewUser: boolean;
  newUser?: any;

  // Product data
  immigrationProductId?: string;
  originalPrice: number;

  // Discount data
  discountAmount?: number;
  discountedPrice: number;

  // Payment data
  paymentMethod?: string;
  paymentType?: string;
  transactionId?: string; // Transaction ID for non-Stripe payments
  payments?: string[]; // Array of payment IDs
  additionalDiscountAmount?: number;

  // Workflow template data
  workflowTemplateId: string;

  // Agent assignment data
  assignedAgentId?: string;
  assignedAgents?: string[]; // Array of agent IDs
  priorityLevel: string;
  notes?: string;
}

/**
 * Application Creation Workflow Steps (v2.0.0 - 2025-07-08)
 *
 * Streamlined workflow with automated payment processing:
 *
 * Step 1: User Selection - Select existing user or create new user
 * Step 2: Product Selection - Choose immigration service/product
 * Step 3: Discount Application - Apply discounts if needed
 * Step 4: Payment Selection - Configure payment details
 *   - Stripe: Auto-completes application and redirects to records
 *   - Non-Stripe: Continues to agent assignment
 * Step 5: Agent Assignment - Assign agent and finalize (non-Stripe only)
 *
 * Key Changes:
 * - Removed workflow template selection step (auto-assigned)
 * - Stripe payments auto-complete entire application process
 * - Transaction ID made optional for all payment methods
 * - Discount amount validation fixed for integer values
 */
const steps = [
  { id: 1, name: "User Selection", description: "Select or create user" },
  {
    id: 2,
    name: "Product Selection",
    description: "Choose immigration product",
  },
  {
    id: 3,
    name: "Discount Application",
    description: "Apply discount if needed",
  },
  {
    id: 4,
    name: "Payment Selection",
    description: "Configure payment details",
  },
  { id: 5, name: "Agent Assignment", description: "Assign agent and finalize" },
];

const CreateApplicationPage: React.FC = () => {
  const router = useRouter();
  const { status } = useSession();
  const { data: profileData } = useProfile();

  // Mutations
  const createApplicationMutation = useCreateApplication();
  const createUserMutation = useCreateUserForApplication();

  const [currentStep, setCurrentStep] = useState(1);
  const [formData, setFormData] = useState<Partial<ApplicationFormData>>({
    createNewUser: false,
    originalPrice: 0,
    discountedPrice: 0,
    workflowTemplateId: "",
    priorityLevel: "Medium",
  });
  const [isSubmitting, setIsSubmitting] = useState(false);

  const progress = (currentStep / steps.length) * 100;

  // Authentication check
  useEffect(() => {
    if (status === "loading") {
      return; // Still loading session
    }

    if (status === "unauthenticated") {
      // Redirect to signin with return URL
      const returnUrl = encodeURIComponent("/applications/new");
      router.push(`/signin?callbackUrl=${returnUrl}`);
      return;
    }
  }, [status, router]);

  // Show loading while checking authentication
  if (status === "loading") {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <p>Loading...</p>
        </div>
      </div>
    );
  }

  // Don't render if not authenticated
  if (status === "unauthenticated") {
    return null;
  }

  const handleUserSelection = (data: any) => {
    setFormData((prev) => ({
      ...prev,
      userId: data.userId,
      createNewUser: data.createNewUser,
      newUser: data.newUser,
    }));
    setCurrentStep(2);
  };

  const handleProductSelection = (data: any) => {
    setFormData((prev) => ({
      ...prev,
      immigrationProductId: data.immigrationProductId,
      originalPrice: data.originalPrice,
      discountedPrice: data.originalPrice, // Initialize with original price
    }));
    setCurrentStep(3);
  };

  /**
   * Handle discount application step completion
   * Stores pricing information and proceeds to payment selection (step 4)
   * @param {any} data - The discount application form data containing pricing information
   */
  const handleDiscountApplication = (data: any) => {
    setFormData((prev) => ({
      ...prev,
      originalPrice: data.originalPrice,
      discountAmount: data.discountAmount,
      discountedPrice: data.discountedPrice,
    }));
    setCurrentStep(4); // Now goes to Payment Selection
  };

  /**
   * Handle payment selection step completion
   *
   * This function implements the core workflow automation logic:
   * 1. For Stripe payments: Fetches default workflow template, auto-completes application, redirects to records
   * 2. For non-Stripe payments: Stores payment data and proceeds to agent assignment
   *
   * Stripe Auto-Completion Process:
   * - Fetches first available active workflow template
   * - Creates application with default settings (no agent assignment)
   * - Redirects user to /applications page
   * - Handles errors gracefully by falling back to manual workflow
   *
   * @param {any} data - The payment selection form data containing payment details
   * @param {string} data.paymentMethod - Payment method selected by user
   * @param {string} data.paymentType - Type of payment (Stripe, Cash, etc.)
   * @param {string} data.paymentId - Generated payment ID from API
   * @param {string[]} data.payments - Array of payment IDs
   * @param {boolean} data.autoComplete - Flag indicating Stripe auto-completion
   */
  const handlePaymentSelection = async (data: any) => {
    const updatedFormData = {
      ...formData,
      paymentMethod: data.paymentMethod,
      paymentType: data.paymentType,
      transactionId: data.transactionId,
      payments: data.payments || [],
    };

    setFormData(updatedFormData);

    // For Stripe payments: Auto-complete application and redirect to records page
    if (data.autoComplete && data.paymentType === "Stripe") {
      try {
        // Fetch the first available workflow template for auto-completion
        const response = await fetch("/api/workflow-templates?page=1&limit=1&isActive=true");
        const templatesData = await response.json();

        let defaultWorkflowTemplateId = "";
        if (templatesData.data && templatesData.data.length > 0) {
          defaultWorkflowTemplateId = templatesData.data[0].id;
        } else {
          throw new Error("No workflow templates available");
        }

        const finalFormData = {
          ...updatedFormData,
          workflowTemplateId: defaultWorkflowTemplateId,
          assignedAgent: [], // No agent assignment for auto-completion
        };

        await submitApplication(finalFormData as ApplicationFormData);

        // Redirect to application records page
        router.push("/applications");
        return;
      } catch (error) {
        console.error("Error auto-completing Stripe application:", error);
        // Fall back to manual workflow if auto-completion fails
        setCurrentStep(5); // Go to agent assignment
        return;
      }
    }

    // For non-Stripe payments: Continue to agent assignment
    setCurrentStep(5); // Goes to Agent Assignment (workflow template auto-selected)
  };

  // Workflow template selection step removed - templates are now auto-selected

  /**
   * Handle agent assignment step completion (final step for non-Stripe payments)
   *
   * This function completes the application creation process for non-Stripe payments:
   * 1. Fetches default workflow template if not already assigned
   * 2. Combines all form data with agent assignment details
   * 3. Submits application to backend API
   * 4. Redirects to applications page upon success
   *
   * Workflow Template Auto-Assignment:
   * - Fetches first available active workflow template from API
   * - Ensures all applications have a valid workflow template
   * - Provides fallback error handling if no templates available
   *
   * @param {any} data - Agent assignment form data
   * @param {string} data.assignedAgentId - Selected agent ID
   * @param {string[]} data.assignedAgents - Array of assigned agent IDs
   * @param {string} data.priorityLevel - Application priority level
   * @param {string} data.notes - Additional notes for the application
   */
  const handleAgentAssignment = async (data: any) => {
    try {
      // Auto-fetch workflow template for non-Stripe payments (Stripe payments already have this set)
      let workflowTemplateId = formData.workflowTemplateId;
      if (!workflowTemplateId) {
        const response = await fetch("/api/workflow-templates?page=1&limit=1&isActive=true");
        const templatesData = await response.json();

        if (templatesData.data && templatesData.data.length > 0) {
          workflowTemplateId = templatesData.data[0].id;
        } else {
          throw new Error("No workflow templates available");
        }
      }

      const finalFormData = {
        ...formData,
        workflowTemplateId,
        assignedAgentId: data.assignedAgentId,
        assignedAgents:
          data.assignedAgents ||
          (data.assignedAgentId ? [data.assignedAgentId] : undefined), // Support both single and multiple agents
        priorityLevel: data.priorityLevel,
        notes: data.notes,
      };

      await submitApplication(finalFormData as ApplicationFormData);

      // Redirect to application records page after successful submission
      router.push("/applications");
    } catch (error) {
      console.error("Error completing application:", error);
      // Handle error appropriately - could show error message to user
    }
  };

  const submitApplication = async (data: ApplicationFormData) => {
    setIsSubmitting(true);
    try {
      // Handle user creation/selection first
      let userId = data.userId;
      if (data.createNewUser && data.newUser) {
        const userData = await createUserMutation.mutateAsync(data.newUser);
        userId = userData.data?.id || userData.id;
      }

      // Prepare the request payload using new API structure
      const payload = {
        service_type: "immigration",
        service_id: data.immigrationProductId || "", // Use immigration product ID as service ID
        user_id: userId || "",
        priority_level: data.priorityLevel,
        workflow_template_id: data.workflowTemplateId,
        payments: data.payments || [], // Use payments array from form data
        assigned_agent:
          data.assignedAgents ||
          (data.assignedAgentId ? [data.assignedAgentId] : undefined), // Convert single agent to array, optional
      };

      // Create the application
      await createApplicationMutation.mutateAsync(payload);

      // Redirect to applications list
      router.push("/applications");
    } catch (error) {
      console.error("Error creating application:", error);
      // Error handling is done in the mutations
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleBack = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  const renderCurrentStep = () => {
    switch (currentStep) {
      case 1:
        return (
          <UserSelectionForm
            onNext={handleUserSelection}
            initialData={{
              userId: formData.userId,
              createNewUser: formData.createNewUser || false,
              newUser: formData.newUser,
            }}
          />
        );
      case 2:
        return (
          <ProductSelectionForm
            onNext={handleProductSelection}
            onBack={handleBack}
            initialData={{
              immigrationProductId: formData.immigrationProductId,
            }}
          />
        );
      case 3:
        return (
          <DiscountApplicationForm
            onNext={handleDiscountApplication}
            onBack={handleBack}
            initialData={{
              originalPrice: formData.originalPrice || 0,
              discountAmount: formData.discountAmount,
              discountedPrice:
                formData.discountedPrice || formData.originalPrice || 0,
            }}
          />
        );
      case 4:
        // Prepare application data for potential Stripe redirect
        // Note: workflow_template_id will be set after payment in step 5
        const applicationDataForPayment = {
          service_type: "immigration",
          service_id: formData.immigrationProductId || "",
          user_id: formData.userId || "",
          priority_level: "Medium", // Default, will be updated in agent assignment
          workflow_template_id: "", // Will be set after workflow template selection
          assigned_agent: undefined, // Will be set in agent assignment step
        };

        return (
          <PaymentSelectionForm
            onNext={handlePaymentSelection}
            onBack={handleBack}
            originalPrice={formData.originalPrice || 0}
            discountedPrice={formData.discountedPrice || 0}
            userId={formData.userId || ""}
            immigrationServiceId={formData.immigrationProductId || ""}
            applicationData={applicationDataForPayment}
            initialData={{
              paymentMethod: formData.paymentMethod,
              paymentType: formData.paymentType,
              transactionId: formData.transactionId,
            }}
          />
        );
      case 5:
        // Agent Assignment (workflow template auto-selected for non-Stripe payments)
        return (
          <AgentAssignmentForm
            onNext={handleAgentAssignment}
            onBack={handleBack}
            userRole={
              profileData?.data?.tokenType as "admin" | "agent" | "user"
            }
            currentUserId={profileData?.data?.id}
            initialData={{
              assignedAgentId: formData.assignedAgentId,
              priorityLevel: formData.priorityLevel,
              notes: formData.notes,
            }}
          />
        );
      default:
        return null;
    }
  };

  return (
    <ContentLayout title="Create New Application">
      <div className="flex justify-between items-center mb-6">
        <Breadcrumb>
          <BreadcrumbList>
            <BreadcrumbItem>
              <BreadcrumbLink asChild>
                <Link href="/">Home</Link>
              </BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator />
            <BreadcrumbItem>
              <BreadcrumbLink asChild>
                <Link href="/applications">Applications</Link>
              </BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator />
            <BreadcrumbItem>
              <BreadcrumbPage>Create New</BreadcrumbPage>
            </BreadcrumbItem>
          </BreadcrumbList>
        </Breadcrumb>
        <Button variant="outline" asChild>
          <Link href="/applications">
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Applications
          </Link>
        </Button>
      </div>

      {/* Progress Indicator */}
      <div className="mb-8">
        <div className="flex justify-between items-center mb-2">
          <h2 className="text-lg font-semibold">
            Step {currentStep} of {steps.length}: {steps[currentStep - 1]?.name}
          </h2>
          <span className="text-sm text-muted-foreground">
            {Math.round(progress)}% Complete
          </span>
        </div>
        <Progress value={progress} className="w-full" />
        <p className="text-sm text-muted-foreground mt-1">
          {steps[currentStep - 1]?.description}
        </p>
      </div>

      {/* Current Step Form */}
      <div className="max-w-2xl mx-auto">{renderCurrentStep()}</div>

      {/* Loading Overlay */}
      {isSubmitting && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-white p-6 rounded-lg shadow-lg">
            <div className="flex items-center space-x-3">
              <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
              <span>Creating application...</span>
            </div>
          </div>
        </div>
      )}
    </ContentLayout>
  );
};

export default CreateApplicationPage;
